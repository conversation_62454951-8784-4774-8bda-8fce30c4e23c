/**
 * Message list component for VPS Admin Chat
 */

import React, { useRef, useEffect } from 'react';
import { Message } from '@/types';
import MessageBubble from '@/components/MessageBubble';
import LoadingIndicator from '@/components/LoadingIndicator';

interface MessageListProps {
  messages: Message[];
  showConfirmation: boolean;
  commandToConfirm: string | null;
  isTaskActive: boolean;
  isWaiting: boolean;
  isAwaitingAnswer: boolean;
  onConfirmation: (confirm: 'yes' | 'no' | 'skip_step' | 'retry_step' | 'abort_task') => void;
  className?: string;
}

const MessageList: React.FC<MessageListProps> = ({
  messages,
  showConfirmation,
  commandToConfirm,
  isTaskActive,
  isWaiting,
  isAwaitingAnswer,
  onConfirmation,
  className = ""
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Scroll to bottom when messages change
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(scrollToBottom, [messages]);

  return (
    <div className={`flex-1 overflow-y-auto p-4 space-y-0 bg-theme-primary flex flex-col transition-colors duration-300 ${className}`}>
      {messages.map((message) => (
        <MessageBubble
          key={message.id}
          message={message}
          showConfirmation={showConfirmation}
          commandToConfirm={commandToConfirm}
          isTaskActive={isTaskActive}
          onConfirmation={onConfirmation}
        />
      ))}

      {/* Loading indicator */}
      {isWaiting && !showConfirmation && !isAwaitingAnswer && (
        <LoadingIndicator />
      )}

      {/* Scroll anchor */}
      <div ref={messagesEndRef} />
    </div>
  );
};

export default MessageList;
