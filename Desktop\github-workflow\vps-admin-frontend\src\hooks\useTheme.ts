/**
 * Theme management hook for VPS Admin Chat
 * Handles light and dark theme switching
 */

import { useEffect } from 'react';
import { useAppStore } from '@/store/appStore';

export const useTheme = () => {
  const { theme, setTheme } = useAppStore();

  // Apply theme to document root
  useEffect(() => {
    const root = document.documentElement;
    if (theme === 'dark') {
      root.classList.add('dark');
    } else {
      root.classList.remove('dark');
    }

    // Enable transitions after initial theme is applied
    // This prevents transition flashing on page load
    setTimeout(() => {
      document.body.classList.add('theme-transitions-enabled');
    }, 100);
  }, [theme]);

  const toggleTheme = () => {
    if (theme === 'light') {
      setTheme('dark');
    } else {
      setTheme('light');
    }
  };

  const getThemeIcon = () => {
    if (theme === 'light') {
      return 'Sun';
    } else {
      return 'Moon';
    }
  };

  const getThemeLabel = () => {
    return theme.charAt(0).toUpperCase() + theme.slice(1);
  };

  return {
    theme,
    effectiveTheme: theme,
    setTheme,
    toggleTheme,
    getThemeIcon,
    getThemeLabel,
  };
};
