[{"name": "generate-buildid", "duration": 202, "timestamp": 5037808463, "id": 4, "parentId": 1, "tags": {}, "startTime": 1749633090089, "traceId": "5fb4708385a45894"}, {"name": "load-custom-routes", "duration": 2032, "timestamp": 5037808761, "id": 5, "parentId": 1, "tags": {}, "startTime": 1749633090090, "traceId": "5fb4708385a45894"}, {"name": "create-dist-dir", "duration": 388, "timestamp": 5037895873, "id": 6, "parentId": 1, "tags": {}, "startTime": 1749633090177, "traceId": "5fb4708385a45894"}, {"name": "create-pages-mapping", "duration": 301, "timestamp": 5037912181, "id": 7, "parentId": 1, "tags": {}, "startTime": 1749633090193, "traceId": "5fb4708385a45894"}, {"name": "collect-app-paths", "duration": 2890, "timestamp": 5037912559, "id": 8, "parentId": 1, "tags": {}, "startTime": 1749633090193, "traceId": "5fb4708385a45894"}, {"name": "create-app-mapping", "duration": 1095, "timestamp": 5037915510, "id": 9, "parentId": 1, "tags": {}, "startTime": 1749633090196, "traceId": "5fb4708385a45894"}, {"name": "public-dir-conflict-check", "duration": 803, "timestamp": 5037917438, "id": 10, "parentId": 1, "tags": {}, "startTime": 1749633090198, "traceId": "5fb4708385a45894"}, {"name": "generate-routes-manifest", "duration": 1931, "timestamp": 5037918463, "id": 11, "parentId": 1, "tags": {}, "startTime": 1749633090199, "traceId": "5fb4708385a45894"}, {"name": "create-entrypoints", "duration": 29229, "timestamp": 5037988591, "id": 14, "parentId": 1, "tags": {}, "startTime": 1749633090270, "traceId": "5fb4708385a45894"}, {"name": "generate-webpack-config", "duration": 388689, "timestamp": 5038017870, "id": 15, "parentId": 13, "tags": {}, "startTime": 1749633090299, "traceId": "5fb4708385a45894"}, {"name": "next-trace-entrypoint-plugin", "duration": 2131, "timestamp": 5038541771, "id": 17, "parentId": 16, "tags": {}, "startTime": 1749633090823, "traceId": "5fb4708385a45894"}, {"name": "add-entry", "duration": 368277, "timestamp": 5038550540, "id": 20, "parentId": 18, "tags": {"request": "next/dist/pages/_app"}, "startTime": 1749633090831, "traceId": "5fb4708385a45894"}, {"name": "add-entry", "duration": 403575, "timestamp": 5038550567, "id": 21, "parentId": 18, "tags": {"request": "next-route-loader?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=next%2Fdist%2Fpages%2F_error&absoluteAppPath=next%2Fdist%2Fpages%2F_app&absoluteDocumentPath=next%2Fdist%2Fpages%2F_document&middlewareConfigBase64=e30%3D!"}, "startTime": 1749633090832, "traceId": "5fb4708385a45894"}, {"name": "add-entry", "duration": 436761, "timestamp": 5038550594, "id": 23, "parentId": 18, "tags": {"request": "next/dist/pages/_document"}, "startTime": 1749633090832, "traceId": "5fb4708385a45894"}, {"name": "add-entry", "duration": 459930, "timestamp": 5038550012, "id": 19, "parentId": 18, "tags": {"request": "next-app-loader?page=%2F_not-found%2Fpage&name=app%2F_not-found%2Fpage&pagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&appDir=C%3A%5CUsers%5CMoetez%5CDesktop%5Cgithub-workflow%5Cvps-admin-frontend%5Csrc%5Capp&appPaths=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=standalone&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1749633090831, "traceId": "5fb4708385a45894"}, {"name": "add-entry", "duration": 459377, "timestamp": 5038550582, "id": 22, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fpage&name=app%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CMoetez%5CDesktop%5Cgithub-workflow%5Cvps-admin-frontend%5Csrc%5Capp&appPaths=%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=standalone&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1749633090832, "traceId": "5fb4708385a45894"}, {"name": "make", "duration": 1421495, "timestamp": 5038549667, "id": 18, "parentId": 16, "tags": {}, "startTime": 1749633090831, "traceId": "5fb4708385a45894"}, {"name": "get-entries", "duration": 1088, "timestamp": 5039972500, "id": 35, "parentId": 34, "tags": {}, "startTime": 1749633092253, "traceId": "5fb4708385a45894"}, {"name": "node-file-trace-plugin", "duration": 64587, "timestamp": 5039976015, "id": 36, "parentId": 34, "tags": {"traceEntryCount": "6"}, "startTime": 1749633092257, "traceId": "5fb4708385a45894"}, {"name": "collect-traced-files", "duration": 382, "timestamp": 5040040617, "id": 37, "parentId": 34, "tags": {}, "startTime": 1749633092322, "traceId": "5fb4708385a45894"}, {"name": "finish-modules", "duration": 68712, "timestamp": 5039972295, "id": 34, "parentId": 17, "tags": {}, "startTime": 1749633092253, "traceId": "5fb4708385a45894"}, {"name": "chunk-graph", "duration": 8889, "timestamp": 5040104080, "id": 39, "parentId": 38, "tags": {}, "startTime": 1749633092385, "traceId": "5fb4708385a45894"}, {"name": "optimize-modules", "duration": 32, "timestamp": 5040113106, "id": 41, "parentId": 38, "tags": {}, "startTime": 1749633092394, "traceId": "5fb4708385a45894"}, {"name": "optimize-chunks", "duration": 12566, "timestamp": 5040113234, "id": 42, "parentId": 38, "tags": {}, "startTime": 1749633092394, "traceId": "5fb4708385a45894"}, {"name": "optimize-tree", "duration": 158, "timestamp": 5040125893, "id": 43, "parentId": 38, "tags": {}, "startTime": 1749633092407, "traceId": "5fb4708385a45894"}, {"name": "optimize-chunk-modules", "duration": 7350, "timestamp": 5040126165, "id": 44, "parentId": 38, "tags": {}, "startTime": 1749633092407, "traceId": "5fb4708385a45894"}, {"name": "optimize", "duration": 20577, "timestamp": 5040113051, "id": 40, "parentId": 38, "tags": {}, "startTime": 1749633092394, "traceId": "5fb4708385a45894"}, {"name": "module-hash", "duration": 10695, "timestamp": 5040150217, "id": 45, "parentId": 38, "tags": {}, "startTime": 1749633092431, "traceId": "5fb4708385a45894"}, {"name": "code-generation", "duration": 3190, "timestamp": 5040160971, "id": 46, "parentId": 38, "tags": {}, "startTime": 1749633092442, "traceId": "5fb4708385a45894"}, {"name": "hash", "duration": 8079, "timestamp": 5040168717, "id": 47, "parentId": 38, "tags": {}, "startTime": 1749633092450, "traceId": "5fb4708385a45894"}, {"name": "code-generation-jobs", "duration": 323, "timestamp": 5040176794, "id": 48, "parentId": 38, "tags": {}, "startTime": 1749633092458, "traceId": "5fb4708385a45894"}, {"name": "module-assets", "duration": 558, "timestamp": 5040177054, "id": 49, "parentId": 38, "tags": {}, "startTime": 1749633092458, "traceId": "5fb4708385a45894"}, {"name": "create-chunk-assets", "duration": 1331, "timestamp": 5040177629, "id": 50, "parentId": 38, "tags": {}, "startTime": 1749633092459, "traceId": "5fb4708385a45894"}, {"name": "minify-js", "duration": 222, "timestamp": 5040188247, "id": 52, "parentId": 51, "tags": {"name": "../app/_not-found/page.js", "cache": "HIT"}, "startTime": 1749633092469, "traceId": "5fb4708385a45894"}, {"name": "minify-js", "duration": 78, "timestamp": 5040188404, "id": 53, "parentId": 51, "tags": {"name": "../pages/_app.js", "cache": "HIT"}, "startTime": 1749633092469, "traceId": "5fb4708385a45894"}, {"name": "minify-js", "duration": 66, "timestamp": 5040188418, "id": 54, "parentId": 51, "tags": {"name": "../pages/_error.js", "cache": "HIT"}, "startTime": 1749633092469, "traceId": "5fb4708385a45894"}, {"name": "minify-js", "duration": 58, "timestamp": 5040188428, "id": 55, "parentId": 51, "tags": {"name": "../app/page.js", "cache": "HIT"}, "startTime": 1749633092469, "traceId": "5fb4708385a45894"}, {"name": "minify-js", "duration": 51, "timestamp": 5040188436, "id": 56, "parentId": 51, "tags": {"name": "../pages/_document.js", "cache": "HIT"}, "startTime": 1749633092469, "traceId": "5fb4708385a45894"}, {"name": "minify-js", "duration": 44, "timestamp": 5040188445, "id": 57, "parentId": 51, "tags": {"name": "../webpack-runtime.js", "cache": "HIT"}, "startTime": 1749633092469, "traceId": "5fb4708385a45894"}, {"name": "minify-js", "duration": 37, "timestamp": 5040188454, "id": 58, "parentId": 51, "tags": {"name": "776.js", "cache": "HIT"}, "startTime": 1749633092469, "traceId": "5fb4708385a45894"}, {"name": "minify-js", "duration": 30, "timestamp": 5040188462, "id": 59, "parentId": 51, "tags": {"name": "548.js", "cache": "HIT"}, "startTime": 1749633092469, "traceId": "5fb4708385a45894"}, {"name": "minify-webpack-plugin-optimize", "duration": 3816, "timestamp": 5040184688, "id": 51, "parentId": 16, "tags": {"compilationName": "server", "mangle": "true"}, "startTime": 1749633092466, "traceId": "5fb4708385a45894"}, {"name": "css-minimizer-plugin", "duration": 144, "timestamp": 5040188634, "id": 60, "parentId": 16, "tags": {}, "startTime": 1749633092470, "traceId": "5fb4708385a45894"}, {"name": "create-trace-assets", "duration": 1123, "timestamp": 5040189051, "id": 61, "parentId": 17, "tags": {}, "startTime": 1749633092470, "traceId": "5fb4708385a45894"}, {"name": "seal", "duration": 116187, "timestamp": 5040082123, "id": 38, "parentId": 16, "tags": {}, "startTime": 1749633092363, "traceId": "5fb4708385a45894"}, {"name": "webpack-compilation", "duration": 1661817, "timestamp": 5038539948, "id": 16, "parentId": 13, "tags": {"name": "server"}, "startTime": 1749633090821, "traceId": "5fb4708385a45894"}, {"name": "emit", "duration": 20613, "timestamp": 5040202176, "id": 62, "parentId": 13, "tags": {}, "startTime": 1749633092483, "traceId": "5fb4708385a45894"}, {"name": "webpack-close", "duration": 814, "timestamp": 5040224971, "id": 63, "parentId": 13, "tags": {"name": "server"}, "startTime": 1749633092506, "traceId": "5fb4708385a45894"}, {"name": "webpack-generate-error-stats", "duration": 2621, "timestamp": 5040225850, "id": 64, "parentId": 63, "tags": {}, "startTime": 1749633092507, "traceId": "5fb4708385a45894"}, {"name": "make", "duration": 265, "timestamp": 5040239421, "id": 66, "parentId": 65, "tags": {}, "startTime": 1749633092520, "traceId": "5fb4708385a45894"}, {"name": "chunk-graph", "duration": 31, "timestamp": 5040240377, "id": 68, "parentId": 67, "tags": {}, "startTime": 1749633092521, "traceId": "5fb4708385a45894"}, {"name": "optimize-modules", "duration": 10, "timestamp": 5040240474, "id": 70, "parentId": 67, "tags": {}, "startTime": 1749633092521, "traceId": "5fb4708385a45894"}, {"name": "optimize-chunks", "duration": 72, "timestamp": 5040240551, "id": 71, "parentId": 67, "tags": {}, "startTime": 1749633092521, "traceId": "5fb4708385a45894"}, {"name": "optimize-tree", "duration": 11, "timestamp": 5040240674, "id": 72, "parentId": 67, "tags": {}, "startTime": 1749633092522, "traceId": "5fb4708385a45894"}, {"name": "optimize-chunk-modules", "duration": 57, "timestamp": 5040240765, "id": 73, "parentId": 67, "tags": {}, "startTime": 1749633092522, "traceId": "5fb4708385a45894"}, {"name": "optimize", "duration": 437, "timestamp": 5040240431, "id": 69, "parentId": 67, "tags": {}, "startTime": 1749633092521, "traceId": "5fb4708385a45894"}, {"name": "module-hash", "duration": 17, "timestamp": 5040241085, "id": 74, "parentId": 67, "tags": {}, "startTime": 1749633092522, "traceId": "5fb4708385a45894"}, {"name": "code-generation", "duration": 14, "timestamp": 5040241118, "id": 75, "parentId": 67, "tags": {}, "startTime": 1749633092522, "traceId": "5fb4708385a45894"}, {"name": "hash", "duration": 90, "timestamp": 5040241184, "id": 76, "parentId": 67, "tags": {}, "startTime": 1749633092522, "traceId": "5fb4708385a45894"}, {"name": "code-generation-jobs", "duration": 57, "timestamp": 5040241274, "id": 77, "parentId": 67, "tags": {}, "startTime": 1749633092522, "traceId": "5fb4708385a45894"}, {"name": "module-assets", "duration": 22, "timestamp": 5040241323, "id": 78, "parentId": 67, "tags": {}, "startTime": 1749633092522, "traceId": "5fb4708385a45894"}, {"name": "create-chunk-assets", "duration": 15, "timestamp": 5040241353, "id": 79, "parentId": 67, "tags": {}, "startTime": 1749633092522, "traceId": "5fb4708385a45894"}, {"name": "minify-js", "duration": 70, "timestamp": 5040247711, "id": 81, "parentId": 80, "tags": {"name": "interception-route-rewrite-manifest.js", "cache": "HIT"}, "startTime": 1749633092529, "traceId": "5fb4708385a45894"}, {"name": "minify-webpack-plugin-optimize", "duration": 1252, "timestamp": 5040246551, "id": 80, "parentId": 65, "tags": {"compilationName": "edge-server", "mangle": "true"}, "startTime": 1749633092527, "traceId": "5fb4708385a45894"}, {"name": "css-minimizer-plugin", "duration": 13, "timestamp": 5040247892, "id": 82, "parentId": 65, "tags": {}, "startTime": 1749633092529, "traceId": "5fb4708385a45894"}, {"name": "seal", "duration": 9539, "timestamp": 5040240220, "id": 67, "parentId": 65, "tags": {}, "startTime": 1749633092521, "traceId": "5fb4708385a45894"}, {"name": "webpack-compilation", "duration": 12408, "timestamp": 5040237458, "id": 65, "parentId": 13, "tags": {"name": "edge-server"}, "startTime": 1749633092518, "traceId": "5fb4708385a45894"}, {"name": "emit", "duration": 2729, "timestamp": 5040249933, "id": 83, "parentId": 13, "tags": {}, "startTime": 1749633092531, "traceId": "5fb4708385a45894"}, {"name": "webpack-close", "duration": 355, "timestamp": 5040252973, "id": 84, "parentId": 13, "tags": {"name": "edge-server"}, "startTime": 1749633092534, "traceId": "5fb4708385a45894"}, {"name": "webpack-generate-error-stats", "duration": 703, "timestamp": 5040253347, "id": 85, "parentId": 84, "tags": {}, "startTime": 1749633092534, "traceId": "5fb4708385a45894"}, {"name": "add-entry", "duration": 334108, "timestamp": 5040263437, "id": 91, "parentId": 87, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&page=%2F_not-found%2Fpage!"}, "startTime": 1749633092544, "traceId": "5fb4708385a45894"}, {"name": "add-entry", "duration": 355410, "timestamp": 5040263451, "id": 92, "parentId": 87, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_app&page=%2F_app!"}, "startTime": 1749633092544, "traceId": "5fb4708385a45894"}, {"name": "add-entry", "duration": 360762, "timestamp": 5040263519, "id": 94, "parentId": 87, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_error&page=%2F_error!"}, "startTime": 1749633092544, "traceId": "5fb4708385a45894"}, {"name": "add-entry", "duration": 382336, "timestamp": 5040263459, "id": 93, "parentId": 87, "tags": {"request": "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-frontend\\node_modules\\next\\dist\\client\\router.js"}, "startTime": 1749633092544, "traceId": "5fb4708385a45894"}, {"name": "add-entry", "duration": 412477, "timestamp": 5040263427, "id": 90, "parentId": 87, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMoetez%5C%5CDesktop%5C%5Cgithub-workflow%5C%5Cvps-admin-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMoetez%5C%5CDesktop%5C%5Cgithub-workflow%5C%5Cvps-admin-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMoetez%5C%5CDesktop%5C%5Cgithub-workflow%5C%5Cvps-admin-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMoetez%5C%5CDesktop%5C%5Cgithub-workflow%5C%5Cvps-admin-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMoetez%5C%5CDesktop%5C%5Cgithub-workflow%5C%5Cvps-admin-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMoetez%5C%5CDesktop%5C%5Cgithub-workflow%5C%5Cvps-admin-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMoetez%5C%5CDesktop%5C%5Cgithub-workflow%5C%5Cvps-admin-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMoetez%5C%5CDesktop%5C%5Cgithub-workflow%5C%5Cvps-admin-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1749633092544, "traceId": "5fb4708385a45894"}, {"name": "add-entry", "duration": 414711, "timestamp": 5040263411, "id": 89, "parentId": 87, "tags": {"request": "./node_modules/next/dist/client/app-next.js"}, "startTime": 1749633092544, "traceId": "5fb4708385a45894"}, {"name": "add-entry", "duration": 424409, "timestamp": 5040263347, "id": 88, "parentId": 87, "tags": {"request": "./node_modules/next/dist/client/next.js"}, "startTime": 1749633092544, "traceId": "5fb4708385a45894"}, {"name": "postcss-process", "duration": 259421, "timestamp": 5041242984, "id": 100, "parentId": 99, "tags": {}, "startTime": 1749633093524, "traceId": "5fb4708385a45894"}, {"name": "postcss-loader", "duration": 822214, "timestamp": 5040680322, "id": 99, "parentId": 98, "tags": {}, "startTime": 1749633092961, "traceId": "5fb4708385a45894"}, {"name": "css-loader", "duration": 52332, "timestamp": 5041502789, "id": 101, "parentId": 98, "tags": {"astUsed": "true"}, "startTime": 1749633093784, "traceId": "5fb4708385a45894"}, {"name": "build-module-css", "duration": 1380231, "timestamp": 5040651273, "id": 98, "parentId": 97, "tags": {"name": "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-frontend\\src\\app\\globals.css.webpack[javascript/auto]!=!C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-frontend\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[2]!C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-frontend\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[13].oneOf[10].use[3]!C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-frontend\\src\\app\\globals.css", "layer": null}, "startTime": 1749633092932, "traceId": "5fb4708385a45894"}, {"name": "build-module-css", "duration": 1408630, "timestamp": 5040636564, "id": 97, "parentId": 86, "tags": {"name": "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-frontend\\src\\app\\globals.css", "layer": "app-pages-browser"}, "startTime": 1749633092918, "traceId": "5fb4708385a45894"}, {"name": "build-module", "duration": 191, "timestamp": 5042051823, "id": 102, "parentId": 97, "tags": {}, "startTime": 1749633094333, "traceId": "5fb4708385a45894"}, {"name": "add-entry", "duration": 1789251, "timestamp": 5040263527, "id": 95, "parentId": 87, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMoetez%5C%5CDesktop%5C%5Cgithub-workflow%5C%5Cvps-admin-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMoetez%5C%5CDesktop%5C%5Cgithub-workflow%5C%5Cvps-admin-frontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMoetez%5C%5CDesktop%5C%5Cgithub-workflow%5C%5Cvps-admin-frontend%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=false!"}, "startTime": 1749633092544, "traceId": "5fb4708385a45894"}, {"name": "add-entry", "duration": 1826633, "timestamp": **********, "id": 96, "parentId": 87, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMoetez%5C%5CDesktop%5C%5Cgithub-workflow%5C%5Cvps-admin-frontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1749633092544, "traceId": "5fb4708385a45894"}, {"name": "make", "duration": 1827244, "timestamp": **********, "id": 87, "parentId": 86, "tags": {}, "startTime": 1749633092544, "traceId": "5fb4708385a45894"}, {"name": "chunk-graph", "duration": 26889, "timestamp": **********, "id": 104, "parentId": 103, "tags": {}, "startTime": 1749633094424, "traceId": "5fb4708385a45894"}, {"name": "optimize-modules", "duration": 11, "timestamp": **********, "id": 106, "parentId": 103, "tags": {}, "startTime": 1749633094451, "traceId": "5fb4708385a45894"}, {"name": "optimize-chunks", "duration": 17423, "timestamp": 5042171649, "id": 108, "parentId": 103, "tags": {}, "startTime": 1749633094453, "traceId": "5fb4708385a45894"}, {"name": "optimize-tree", "duration": 14, "timestamp": 5042189130, "id": 109, "parentId": 103, "tags": {}, "startTime": 1749633094470, "traceId": "5fb4708385a45894"}, {"name": "optimize-chunk-modules", "duration": 31843, "timestamp": 5042189190, "id": 110, "parentId": 103, "tags": {}, "startTime": 1749633094470, "traceId": "5fb4708385a45894"}, {"name": "optimize", "duration": 51318, "timestamp": 5042169766, "id": 105, "parentId": 103, "tags": {}, "startTime": 1749633094451, "traceId": "5fb4708385a45894"}, {"name": "module-hash", "duration": 25980, "timestamp": 5042242795, "id": 111, "parentId": 103, "tags": {}, "startTime": 1749633094524, "traceId": "5fb4708385a45894"}, {"name": "code-generation", "duration": 5037, "timestamp": 5042268821, "id": 112, "parentId": 103, "tags": {}, "startTime": 1749633094550, "traceId": "5fb4708385a45894"}, {"name": "hash", "duration": 10509, "timestamp": 5042280109, "id": 113, "parentId": 103, "tags": {}, "startTime": 1749633094561, "traceId": "5fb4708385a45894"}, {"name": "code-generation-jobs", "duration": 177, "timestamp": 5042290616, "id": 114, "parentId": 103, "tags": {}, "startTime": 1749633094572, "traceId": "5fb4708385a45894"}, {"name": "module-assets", "duration": 761, "timestamp": 5042290757, "id": 115, "parentId": 103, "tags": {}, "startTime": 1749633094572, "traceId": "5fb4708385a45894"}, {"name": "create-chunk-assets", "duration": 3059, "timestamp": 5042291539, "id": 116, "parentId": 103, "tags": {}, "startTime": 1749633094572, "traceId": "5fb4708385a45894"}, {"name": "NextJsBuildManifest-generateClientManifest", "duration": 1638, "timestamp": 5042300797, "id": 118, "parentId": 86, "tags": {}, "startTime": 1749633094582, "traceId": "5fb4708385a45894"}, {"name": "NextJsBuildManifest-createassets", "duration": 2590, "timestamp": 5042299880, "id": 117, "parentId": 86, "tags": {}, "startTime": 1749633094581, "traceId": "5fb4708385a45894"}, {"name": "minify-js", "duration": 1048, "timestamp": 5042310156, "id": 120, "parentId": 119, "tags": {"name": "static/chunks/main-05485b9cbcd8a7a6.js", "cache": "HIT"}, "startTime": 1749633094591, "traceId": "5fb4708385a45894"}]