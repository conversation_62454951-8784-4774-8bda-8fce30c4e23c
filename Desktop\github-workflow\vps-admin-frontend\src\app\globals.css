@tailwind base;
@tailwind components;
@tailwind utilities;

/* Tailwind v4 Dark Mode Configuration */
@theme {
  --color-primary: 20 184 166; /* teal-500 */
  --color-primary-dark: 15 118 110; /* teal-700 */
  --color-success: 34 197 94; /* green-500 */
  --color-error: 239 68 68; /* red-500 */
  --color-warning: 245 158 11; /* amber-500 */
  --color-info: 59 130 246; /* blue-500 */

  /* Animation durations */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --duration-slow: 350ms;

  /* Easing functions */
  --ease-out: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Enhanced CSS Variables for Dynamic Theming */
:root {
  /* Light theme colors */
  --bg-primary: 255 255 255; /* white */
  --bg-secondary: 249 250 251; /* gray-50 */
  --bg-tertiary: 243 244 246; /* gray-100 */
  --bg-quaternary: 229 231 235; /* gray-200 */
  --text-primary: 17 24 39; /* gray-900 */
  --text-secondary: 75 85 99; /* gray-600 */
  --text-tertiary: 156 163 175; /* gray-400 */
  --text-quaternary: 107 114 128; /* gray-500 */
  --border-primary: 229 231 235; /* gray-200 */
  --border-secondary: 209 213 219; /* gray-300 */
  --border-tertiary: 156 163 175; /* gray-400 */

  /* Light theme accent colors */
  --accent-primary: 59 130 246; /* blue-500 */
  --accent-secondary: 34 197 94; /* emerald-500 */
  --accent-tertiary: 168 85 247; /* purple-500 */
  --accent-warning: 245 158 11; /* amber-500 */
  --accent-error: 239 68 68; /* red-500 */

  /* Light theme surface colors for cards and panels */
  --surface-primary: 255 255 255; /* white */
  --surface-secondary: 249 250 251; /* gray-50 */
  --surface-tertiary: 243 244 246; /* gray-100 */

  /* Light theme interactive element colors */
  --interactive-primary: 59 130 246; /* blue-500 */
  --interactive-secondary: 243 244 246; /* gray-100 */
  --interactive-hover: 229 231 235; /* gray-200 */

  /* Light theme terminal colors - mimicking classic terminal appearance */
  --terminal-bg: 248 250 252; /* slate-50 - light terminal background */
  --terminal-text: 30 41 59; /* slate-800 - dark text on light background */
  --terminal-border: 203 213 225; /* slate-300 - subtle border */
  --terminal-success-bg: 240 253 244; /* green-50 - light green background */
  --terminal-success-text: 22 101 52; /* green-800 - dark green text */
  --terminal-success-border: 34 197 94; /* green-500 - green border */
  --terminal-error-bg: 254 242 242; /* red-50 - light red background */
  --terminal-error-text: 153 27 27; /* red-800 - dark red text */
  --terminal-error-border: 239 68 68; /* red-500 - red border */
  --terminal-header-bg: 241 245 249; /* slate-100 - header background */
  --terminal-header-text: 51 65 85; /* slate-700 - header text */
}

/* Dark theme colors - Enhanced with better contrast and appealing colors */
:root.dark {
  --bg-primary: 15 23 42; /* slate-900 - Deep dark blue */
  --bg-secondary: 30 41 59; /* slate-800 - Rich dark blue-gray */
  --bg-tertiary: 51 65 85; /* slate-700 - Medium dark blue-gray */
  --bg-quaternary: 71 85 105; /* slate-600 - Lighter dark blue-gray */
  --text-primary: 248 250 252; /* slate-50 - Pure white text */
  --text-secondary: 203 213 225; /* slate-300 - Light gray text */
  --text-tertiary: 148 163 184; /* slate-400 - Medium gray text */
  --text-quaternary: 100 116 139; /* slate-500 - Darker gray text */
  --border-primary: 51 65 85; /* slate-700 - Subtle borders */
  --border-secondary: 71 85 105; /* slate-600 - More visible borders */
  --border-tertiary: 100 116 139; /* slate-500 - Prominent borders */

  /* Enhanced accent colors for dark mode */
  --accent-primary: 56 189 248; /* sky-400 - Bright blue accent */
  --accent-secondary: 34 197 94; /* emerald-500 - Green accent */
  --accent-tertiary: 168 85 247; /* purple-500 - Purple accent */
  --accent-warning: 251 191 36; /* amber-400 - Warning yellow */
  --accent-error: 248 113 113; /* red-400 - Error red */

  /* Surface colors for cards and panels */
  --surface-primary: 30 41 59; /* slate-800 */
  --surface-secondary: 51 65 85; /* slate-700 */
  --surface-tertiary: 71 85 105; /* slate-600 */

  /* Interactive element colors */
  --interactive-primary: 56 189 248; /* sky-400 */
  --interactive-secondary: 71 85 105; /* slate-600 */
  --interactive-hover: 100 116 139; /* slate-500 */

  /* Dark theme terminal colors - authentic dark terminal appearance */
  --terminal-bg: 15 23 42; /* slate-900 - dark terminal background */
  --terminal-text: ***********; /* slate-200 - light text on dark background */
  --terminal-border: 71 85 105; /* slate-600 - subtle border */
  --terminal-success-bg: 20 83 45; /* green-900 - dark green background */
  --terminal-success-text: 134 239 172; /* green-300 - light green text */
  --terminal-success-border: 34 197 94; /* green-500 - green border */
  --terminal-error-bg: 127 29 29; /* red-900 - dark red background */
  --terminal-error-text: 252 165 165; /* red-300 - light red text */
  --terminal-error-border: 239 68 68; /* red-500 - red border */
  --terminal-header-bg: 30 41 59; /* slate-800 - header background */
  --terminal-header-text: 203 213 225; /* slate-300 - header text */
}

/* Enhanced base styles with dark mode support */
html {
  /* Prevent zoom on input focus on iOS */
  -webkit-text-size-adjust: 100%;
  /* Smooth scrolling */
  scroll-behavior: smooth;
}

body {
  /* Improve touch scrolling on mobile */
  -webkit-overflow-scrolling: touch;
  /* Prevent horizontal scroll */
  overflow-x: hidden;
  /* Enhanced font rendering */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* Default theme colors */
  background-color: rgb(var(--bg-primary));
  color: rgb(var(--text-primary));
}

/* Theme transitions - added programmatically after initial load */
body.theme-transitions-enabled {
  transition: background-color var(--duration-normal) var(--ease-out),
              color var(--duration-normal) var(--ease-out);
}

/* Prevent flash of unstyled content by ensuring immediate theme application */
html.dark body {
  background-color: rgb(15 23 42); /* slate-900 - matches --bg-primary in dark mode */
  color: rgb(248 250 252); /* slate-50 - matches --text-primary in dark mode */
}

/* Improve touch targets */
button, [role="button"] {
  touch-action: manipulation;
}

/* Enhanced focus styles */
*:focus-visible {
  outline: 2px solid rgb(var(--color-primary));
  outline-offset: 2px;
  border-radius: 4px;
}

/* Smooth transitions for interactive elements */
button, input, select, textarea {
  transition: all var(--duration-fast) var(--ease-out);
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgb(243 244 246); /* gray-100 */
}

::-webkit-scrollbar-thumb {
  background: rgb(156 163 175); /* gray-400 */
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgb(107 114 128); /* gray-500 */
}

/* Dark mode scrollbar */
:root.dark ::-webkit-scrollbar-track {
  background: rgb(31 41 55); /* gray-800 */
}

:root.dark ::-webkit-scrollbar-thumb {
  background: rgb(75 85 99); /* gray-600 */
}

:root.dark ::-webkit-scrollbar-thumb:hover {
  background: rgb(107 114 128); /* gray-500 */
}

/* Enhanced utilities */
  /* Enhanced touch-friendly utilities */
  .touch-manipulation {
    touch-action: manipulation;
  }

  /* Safe area padding for mobile devices with notches */
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Prevent text selection on UI elements */
  .select-none-touch {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
  }

  /* Enhanced touch targets for mobile */
  .touch-target-large {
    min-height: 44px;
    min-width: 44px;
  }

  .touch-target-medium {
    min-height: 36px;
    min-width: 36px;
  }

  /* Mobile-first responsive utilities */
  @media (max-width: 640px) {
    .mobile-full-width {
      width: 100% !important;
    }

    .mobile-full-height {
      height: 100vh !important;
      max-height: 100vh !important;
    }

    .mobile-no-rounded {
      border-radius: 0 !important;
    }

    .mobile-p-4 {
      padding: 1rem !important;
    }

    .mobile-text-base {
      font-size: 1rem !important;
    }
  }

  /* Enhanced animation utilities */
  .animate-fade-in {
    animation: fadeIn var(--duration-normal) var(--ease-out);
  }

  .animate-slide-up {
    animation: slideUp var(--duration-normal) var(--ease-out);
  }

  .animate-slide-down {
    animation: slideDown var(--duration-normal) var(--ease-out);
  }

  .animate-scale-in {
    animation: scaleIn var(--duration-normal) var(--ease-bounce);
  }

  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  /* Animation delay utilities */
  .animation-delay-200 {
    animation-delay: 200ms;
  }

  .animation-delay-400 {
    animation-delay: 400ms;
  }

  .animation-delay-600 {
    animation-delay: 600ms;
  }

  /* Enhanced modal animations */
  @keyframes modalFadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes modalScaleIn {
    from {
      opacity: 0;
      transform: scale(0.95) translateY(-10px);
    }
    to {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
  }

  .animate-modal-fade-in {
    animation: modalFadeIn var(--duration-normal) var(--ease-out);
  }

  .animate-modal-scale-in {
    animation: modalScaleIn var(--duration-normal) var(--ease-out);
  }

  /* Glass morphism effect */
  .glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .glass-dark {
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Gradient utilities */
  .gradient-primary {
    background: linear-gradient(135deg, rgb(var(--color-primary)), rgb(var(--color-primary-dark)));
  }

  .gradient-success {
    background: linear-gradient(135deg, rgb(var(--color-success)), rgb(34 197 94));
  }

  .gradient-error {
    background: linear-gradient(135deg, rgb(var(--color-error)), rgb(220 38 38));
  }

  /* Enhanced shadow utilities */
  .shadow-glow {
    box-shadow: 0 0 20px rgba(var(--color-primary), 0.3);
  }

  .shadow-glow-success {
    box-shadow: 0 0 20px rgba(var(--color-success), 0.3);
  }

  .shadow-glow-error {
    box-shadow: 0 0 20px rgba(var(--color-error), 0.3);
  }

  /* Enhanced dark mode utilities */
  .bg-theme-primary {
    background-color: rgb(var(--bg-primary));
  }

  .bg-theme-secondary {
    background-color: rgb(var(--bg-secondary));
  }

  .bg-theme-tertiary {
    background-color: rgb(var(--bg-tertiary));
  }

  .bg-theme-quaternary {
    background-color: rgb(var(--bg-quaternary));
  }

  .bg-surface-primary {
    background-color: rgb(var(--surface-primary));
  }

  .bg-surface-secondary {
    background-color: rgb(var(--surface-secondary));
  }

  .bg-surface-tertiary {
    background-color: rgb(var(--surface-tertiary));
  }

  .text-theme-primary {
    color: rgb(var(--text-primary));
  }

  .text-theme-secondary {
    color: rgb(var(--text-secondary));
  }

  .text-theme-tertiary {
    color: rgb(var(--text-tertiary));
  }

  .text-theme-quaternary {
    color: rgb(var(--text-quaternary));
  }

  .border-theme-primary {
    border-color: rgb(var(--border-primary));
  }

  .border-theme-secondary {
    border-color: rgb(var(--border-secondary));
  }

  .border-theme-tertiary {
    border-color: rgb(var(--border-tertiary));
  }

  /* Enhanced accent utilities */
  .text-accent-primary {
    color: rgb(var(--accent-primary));
  }

  .text-accent-secondary {
    color: rgb(var(--accent-secondary));
  }

  .text-accent-tertiary {
    color: rgb(var(--accent-tertiary));
  }

  .text-accent-warning {
    color: rgb(var(--accent-warning));
  }

  .text-accent-error {
    color: rgb(var(--accent-error));
  }

  .bg-accent-primary {
    background-color: rgb(var(--accent-primary));
  }

  .bg-accent-secondary {
    background-color: rgb(var(--accent-secondary));
  }

  /* Terminal background utility */
  .bg-terminal-bg {
    background-color: rgb(var(--terminal-bg)) !important;
    color: rgb(var(--terminal-text)) !important;
  }

  .bg-interactive-primary {
    background-color: rgb(var(--interactive-primary));
  }

  .bg-interactive-secondary {
    background-color: rgb(var(--interactive-secondary));
  }

  .hover\:bg-interactive-hover:hover {
    background-color: rgb(var(--interactive-hover));
  }

  .hover\:bg-theme-tertiary:hover {
    background-color: rgb(var(--bg-tertiary));
  }

  /* Enhanced button styles for dark mode */
  .btn-primary {
    background: linear-gradient(135deg, rgb(var(--color-primary)), rgb(var(--color-primary-dark)));
    color: white;
    border: none;
    transition: all var(--duration-fast) var(--ease-out);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(var(--color-primary), 0.4);
  }

  .btn-secondary {
    background-color: rgb(var(--bg-secondary));
    color: rgb(var(--text-primary));
    border: 1px solid rgb(var(--border-primary));
    transition: all var(--duration-fast) var(--ease-out);
  }

  .btn-secondary:hover {
    background-color: rgb(var(--bg-tertiary));
    border-color: rgb(var(--border-secondary));
  }

  /* Enhanced interactive button styles */
  .btn-interactive {
    background-color: rgb(var(--interactive-secondary));
    color: rgb(var(--text-primary));
    border: 1px solid rgb(var(--border-primary));
    transition: all var(--duration-fast) var(--ease-out);
    border-radius: 0.5rem;
    padding: 0.5rem 1rem;
  }

  .btn-interactive:hover {
    background-color: rgb(var(--interactive-hover));
    border-color: rgb(var(--border-secondary));
    transform: translateY(-1px);
  }

  .btn-accent {
    background-color: rgb(var(--accent-primary));
    color: white;
    border: none;
    transition: all var(--duration-fast) var(--ease-out);
    border-radius: 0.5rem;
    padding: 0.5rem 1rem;
  }

  .btn-accent:hover {
    background-color: rgb(var(--accent-primary));
    opacity: 0.9;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(var(--accent-primary), 0.3);
  }

  /* Enhanced card styles */
  .card {
    background-color: rgb(var(--bg-primary));
    border: 1px solid rgb(var(--border-primary));
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all var(--duration-normal) var(--ease-out);
  }

  .card:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }

  :root.dark .card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
    border-color: rgb(var(--border-primary));
  }

  :root.dark .card:hover {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.5);
    border-color: rgb(var(--border-secondary));
  }

  /* Enhanced message bubble styles for dark mode */
  .message-bubble-ai {
    background-color: rgb(var(--surface-secondary));
    color: rgb(var(--text-primary));
    border: 1px solid rgb(var(--border-primary));
  }

  .message-bubble-system {
    background-color: rgb(var(--surface-tertiary));
    color: rgb(var(--text-primary));
    border: 1px solid rgb(var(--border-secondary));
  }

  .message-bubble-user {
    background: linear-gradient(135deg, rgb(var(--accent-primary)), rgb(var(--accent-primary)));
    color: white;
  }

  /* Ensure proper text contrast in message bubbles */
  .message-bubble-ai *,
  .message-bubble-system * {
    color: inherit !important;
  }

  .message-bubble-user * {
    color: white !important;
  }

  /* Override any hardcoded text colors in markdown content within bubbles */
  .message-bubble-ai .markdown-content,
  .message-bubble-system .markdown-content {
    color: rgb(var(--text-primary));
  }

  .message-bubble-ai .markdown-content *,
  .message-bubble-system .markdown-content * {
    color: rgb(var(--text-primary)) !important;
  }

  .message-bubble-user .markdown-content,
  .message-bubble-user .markdown-content * {
    color: white !important;
  }

  /* Enhanced surface cards for stats panels */
  .card-surface {
    background-color: rgb(var(--surface-primary));
    border: 1px solid rgb(var(--border-primary));
    border-radius: 0.75rem;
    transition: all var(--duration-normal) var(--ease-out);
  }

  .card-surface-secondary {
    background-color: rgb(var(--surface-secondary));
    border: 1px solid rgb(var(--border-primary));
    border-radius: 0.5rem;
    transition: all var(--duration-normal) var(--ease-out);
  }

  .card-surface-tertiary {
    background-color: rgb(var(--surface-tertiary));
    border: 1px solid rgb(var(--border-secondary));
    border-radius: 0.5rem;
    transition: all var(--duration-normal) var(--ease-out);
  }

/* Markdown content improvements */
  /* Better text wrapping for markdown content */
  .markdown-content {
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
  }

  /* Inline code wrapping */
  .markdown-content code {
    word-break: break-word;
    white-space: pre-wrap;
    background-color: rgb(var(--bg-tertiary));
    color: rgb(var(--text-primary));
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-size: 0.875em;
    border: 1px solid rgb(var(--border-primary));
  }

  /* Enhanced code block styling */
  .markdown-content pre {
    background-color: rgb(var(--surface-tertiary));
    color: rgb(var(--text-primary));
    padding: 1rem;
    border-radius: 0.5rem;
    overflow-x: auto;
    margin: 0.5rem 0;
    border: 1px solid rgb(var(--border-primary));
  }

  /* Ensure lists wrap properly */
  .markdown-content li {
    word-wrap: break-word;
    overflow-wrap: break-word;
  }



  /* Enhanced blockquote styling */
  .markdown-content blockquote {
    border-left: 4px solid rgb(var(--color-primary));
    padding-left: 1rem;
    margin: 1rem 0;
    font-style: italic;
    color: rgb(var(--text-secondary));
  }

  /* Enhanced table styling */
  .markdown-content table {
    border-collapse: collapse;
    width: 100%;
    margin: 1rem 0;
  }

  .markdown-content th,
  .markdown-content td {
    border: 1px solid rgb(var(--border-primary));
    padding: 0.5rem;
    text-align: left;
  }

  .markdown-content th {
    background-color: rgb(var(--bg-secondary));
    font-weight: 600;
  }

.clip-path-message-tail {
  clip-path: polygon(0 0, 0% 100%, 100% 0);
}
.clip-path-message-tail-user {
  clip-path: polygon(100% 0, 0 0, 100% 100%);
}

/* Enhanced keyframe animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideUp {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideDown {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes scaleIn {
  from { opacity: 0; transform: scale(0.9); }
  to { opacity: 1; transform: scale(1); }
}

@keyframes shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 5px rgba(var(--color-primary), 0.5); }
  50% { box-shadow: 0 0 20px rgba(var(--color-primary), 0.8); }
}

/* Legacy animation class for backward compatibility */
.animate-fadeIn {
  animation: fadeIn var(--duration-normal) var(--ease-out);
}

/* Mobile-specific improvements */
@media (max-width: 640px) {
  /* Improve scrolling performance on mobile */
  .overflow-y-auto {
    -webkit-overflow-scrolling: touch;
  }

  /* Ensure minimum touch target size for important buttons only */
  .touch-target-large {
    min-height: 44px;
    min-width: 44px;
  }

  /* Fix button sizing issues on mobile */
  button {
    box-sizing: border-box;
  }
}

/* Terminal-style command output styling */
.terminal-output {
  background-color: rgb(var(--terminal-bg));
  color: rgb(var(--terminal-text));
  border: 1px solid rgb(var(--terminal-border));
  border-radius: 0.5rem;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 0.875rem;
  line-height: 1.2;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all var(--duration-fast) var(--ease-out);
  font-feature-settings: "liga" 0, "calt" 0;
  font-variant-ligatures: none;
}

:root.dark .terminal-output {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.terminal-header {
  background-color: rgb(var(--terminal-header-bg));
  color: rgb(var(--terminal-header-text));
  padding: 0.5rem 0.75rem;
  border-bottom: 1px solid rgb(var(--terminal-border));
  font-weight: 600;
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.terminal-content {
  padding: 0.75rem;
  background-color: rgb(var(--terminal-bg));
  color: rgb(var(--terminal-text));
}

.terminal-success {
  background-color: rgb(var(--terminal-success-bg));
  color: rgb(var(--terminal-success-text));
  border-color: rgb(var(--terminal-success-border));
}

.terminal-success .terminal-header {
  background-color: rgb(var(--terminal-success-bg));
  color: rgb(var(--terminal-success-text));
  border-bottom-color: rgb(var(--terminal-success-border));
}

/* Enhanced dark mode terminal success header visibility */
:root.dark .terminal-success .terminal-header {
  background-color: rgb(22 101 52); /* green-800 - lighter than green-900 for better contrast */
  color: rgb(187 247 208); /* green-200 - brighter text for better visibility */
  border-bottom-color: rgb(34 197 94); /* green-500 - keep the border bright */
}

.terminal-error {
  background-color: rgb(var(--terminal-error-bg));
  color: rgb(var(--terminal-error-text));
  border-color: rgb(var(--terminal-error-border));
}

.terminal-error .terminal-header {
  background-color: rgb(var(--terminal-error-bg));
  color: rgb(var(--terminal-error-text));
  border-bottom-color: rgb(var(--terminal-error-border));
}

/* Enhanced dark mode terminal error header visibility */
:root.dark .terminal-error .terminal-header {
  background-color: rgb(153 27 27); /* red-800 - lighter than red-900 for better contrast */
  color: rgb(254 202 202); /* red-200 - brighter text for better visibility */
  border-bottom-color: rgb(239 68 68); /* red-500 - keep the border bright */
}

.terminal-pre {
  background: transparent;
  color: inherit;
  margin: 0;
  padding: 0;
  border: none;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 0.875rem;
  line-height: 1.2;
  white-space: pre;
  word-wrap: normal;
  overflow-wrap: normal;
  max-height: 20rem;
  overflow-y: auto;
  overflow-x: auto;
  tab-size: 8;
  -moz-tab-size: 8;
}

.terminal-details {
  margin: 0.5rem 0;
}

.terminal-details summary {
  cursor: pointer;
  padding: 0.25rem 0.5rem;
  background-color: rgba(var(--terminal-border), 0.1);
  border-radius: 0.25rem;
  font-weight: 500;
  transition: all var(--duration-fast) var(--ease-out);
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.terminal-details summary:hover {
  background-color: rgba(var(--terminal-border), 0.2);
}

.terminal-details[open] summary {
  margin-bottom: 0.5rem;
  border-bottom: 1px solid rgba(var(--terminal-border), 0.3);
}

.terminal-stdout {
  background-color: rgb(var(--terminal-bg));
  color: rgb(var(--terminal-text));
  border: 1px solid rgb(var(--terminal-border));
}

.terminal-stderr {
  background-color: rgb(var(--terminal-error-bg));
  color: rgb(var(--terminal-error-text));
  border: 1px solid rgb(var(--terminal-error-border));
}

/* Terminal scrollbar styling */
.terminal-pre::-webkit-scrollbar {
  width: 6px;
}

.terminal-pre::-webkit-scrollbar-track {
  background: rgba(var(--terminal-border), 0.1);
  border-radius: 3px;
}

.terminal-pre::-webkit-scrollbar-thumb {
  background: rgba(var(--terminal-border), 0.3);
  border-radius: 3px;
}

.terminal-pre::-webkit-scrollbar-thumb:hover {
  background: rgba(var(--terminal-border), 0.5);
}

/* Terminal command styling for syntax highlighting */
.terminal-command {
  border: 1px solid rgb(71 85 105); /* slate-600 */
  border-radius: 0.375rem;
  padding: 0.75rem;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace !important;
  font-size: 0.875rem;
  line-height: 1.4;
  overflow-x: auto;
  position: relative;
  word-wrap: break-word;
  white-space: pre-wrap;
  /* Force dark terminal appearance in both modes */
  background-color: rgb(15 23 42) !important; /* slate-900 - dark background */
  color: rgb(***********) !important; /* slate-200 - light text for contrast */
}

.terminal-command::before {
  content: '$ ';
  color: rgb(56 189 248) !important; /* sky-400 - blue prompt */
  font-weight: 600;
}

/* Ensure all child elements inherit the correct color */
.terminal-command * {
  color: rgb(***********) !important; /* slate-200 - light text */
}

/* Dark mode terminal command styling */
:root.dark .terminal-command {
  background-color: rgb(15 23 42) !important; /* slate-900 - keep dark background */
  color: rgb(***********) !important; /* slate-200 - light text */
  border-color: rgb(71 85 105); /* slate-600 */
}

:root.dark .terminal-command::before {
  color: rgb(56 189 248) !important; /* sky-400 - blue prompt */
}

:root.dark .terminal-command * {
  color: rgb(***********) !important; /* slate-200 - light text */
}

/* Additional specificity for terminal command text visibility */
div.terminal-command,
.terminal-command,
[class*="terminal-command"] {
  background-color: rgb(15 23 42) !important; /* slate-900 */
  color: rgb(***********) !important; /* slate-200 */
}

div.terminal-command *,
.terminal-command *,
[class*="terminal-command"] * {
  color: rgb(***********) !important; /* slate-200 */
}

/* Enhanced terminal status indicators */
.terminal-status-success {
  color: rgb(var(--terminal-success-text));
}

.terminal-status-error {
  color: rgb(var(--terminal-error-text));
}

.terminal-exit-code {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 0.75rem;
  opacity: 0.8;
}

/* Terminal output with modal enhancements */
.terminal-output.group:hover .terminal-modal-button {
  opacity: 1;
}

.terminal-modal-button {
  opacity: 0;
  transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out;
}

.terminal-modal-button:hover {
  transform: scale(1.05);
}

/* Enhanced terminal details styling for better arrow button positioning */
.terminal-details summary {
  position: relative;
  padding-right: 2.5rem; /* Make space for the arrow button */
  cursor: pointer;
  list-style: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.terminal-details summary::-webkit-details-marker {
  display: none;
}

.terminal-details summary::marker {
  display: none;
}

/* Ensure proper spacing and alignment for terminal content */
.terminal-details {
  margin-bottom: 0.5rem;
}

.terminal-details:last-child {
  margin-bottom: 0;
}

/* Modal backdrop blur effect */
.modal-backdrop {
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

/* Smooth modal animations */
.modal-enter {
  opacity: 0;
  transform: scale(0.95);
}

.modal-enter-active {
  opacity: 1;
  transform: scale(1);
  transition: opacity 0.2s ease-out, transform 0.2s ease-out;
}

.modal-exit {
  opacity: 1;
  transform: scale(1);
}

.modal-exit-active {
  opacity: 0;
  transform: scale(0.95);
  transition: opacity 0.15s ease-in, transform 0.15s ease-in;
}

/* Enhanced terminal output formatting for modal */
.terminal-modal-output {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace !important;
  font-size: 0.875rem;
  line-height: 1.3;
  white-space: pre;
  overflow-x: auto;
  overflow-y: auto;
  tab-size: 8;
  -moz-tab-size: 8;
  font-feature-settings: "liga" 0, "calt" 0;
  font-variant-ligatures: none;
  letter-spacing: 0;
  word-spacing: 0;
  /* Ensure proper text color for terminal output */
  color: rgb(var(--terminal-text)) !important;
  background-color: transparent;
  /* Enhanced scrollbar for terminal content */
  scrollbar-width: thin;
  scrollbar-color: rgb(var(--border-secondary)) transparent;
}

.terminal-modal-output::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.terminal-modal-output::-webkit-scrollbar-track {
  background: transparent;
}

.terminal-modal-output::-webkit-scrollbar-thumb {
  background: rgb(var(--border-secondary));
  border-radius: 3px;
}

.terminal-modal-output::-webkit-scrollbar-thumb:hover {
  background: rgb(var(--border-tertiary));
}

/* Ensure proper spacing for terminal commands like ps aux */
.terminal-modal-output.ps-output {
  font-size: 0.8rem;
  line-height: 1.1;
}

/* Enhanced terminal success/error background utilities */
.bg-terminal-success-bg {
  background-color: rgb(var(--terminal-success-bg)) !important;
}

.bg-terminal-error-bg {
  background-color: rgb(var(--terminal-error-bg)) !important;
}

.text-terminal-success-text {
  color: rgb(var(--terminal-success-text)) !important;
}

.text-terminal-error-text {
  color: rgb(var(--terminal-error-text)) !important;
}

.border-terminal-success-border {
  border-color: rgb(var(--terminal-success-border)) !important;
}

.border-terminal-error-border {
  border-color: rgb(var(--terminal-error-border)) !important;
}

/* Additional terminal header visibility fixes for dark mode */
:root.dark .terminal-header {
  background-color: rgb(30 41 59) !important; /* slate-800 - ensure visibility */
  color: rgb(203 213 225) !important; /* slate-300 - bright text */
  border-bottom-color: rgb(71 85 105) !important; /* slate-600 - visible border */
}

/* Ensure terminal exit code is always visible */
:root.dark .terminal-exit-code {
  color: rgb(148 163 184) !important; /* slate-400 - visible but subtle */
  opacity: 1;
}

/* Force terminal header text visibility in all states */
.terminal-header span,
.terminal-header .terminal-exit-code {
  color: inherit !important;
}

:root.dark .terminal-header span,
:root.dark .terminal-header .terminal-exit-code {
  color: rgb(203 213 225) !important; /* slate-300 - ensure visibility */
}

/* Enhanced terminal modal dark mode fixes */
:root.dark .terminal-modal-output {
  color: rgb(***********) !important; /* slate-200 - bright text for dark mode */
}

:root.dark .bg-terminal-bg {
  background-color: rgb(15 23 42) !important; /* slate-900 - dark background */
  color: rgb(***********) !important; /* slate-200 - bright text */
}

/* Ensure all terminal text elements are visible in dark mode */
:root.dark .terminal-pre,
:root.dark .terminal-stdout,
:root.dark .terminal-stderr {
  color: rgb(***********) !important; /* slate-200 - bright text */
  background-color: rgb(15 23 42) !important; /* slate-900 - dark background */
}

/* Force visibility for any nested elements in terminal output */
:root.dark .terminal-modal-output *,
:root.dark .bg-terminal-bg *,
:root.dark .terminal-pre *,
:root.dark .terminal-stdout *,
:root.dark .terminal-stderr * {
  color: rgb(***********) !important; /* slate-200 - ensure all text is visible */
}