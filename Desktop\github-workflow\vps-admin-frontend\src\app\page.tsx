'use client';

import dynamic from 'next/dynamic';
import { Suspense } from 'react';
import LoadingIndicator from '@/components/LoadingIndicator';

// Dynamically import VPSAdminChat to ensure it only runs on client side
const VPSAdminChat = dynamic(() => import('@/components/VPSAdminChat'), {
  ssr: false,
  loading: () => <LoadingIndicator />,
});

export default function HomePage() {
  return (
    <main className="flex items-center justify-center h-screen lg:min-h-screen bg-theme-secondary p-0 lg:p-4 overflow-hidden lg:overflow-auto transition-all duration-500">
      <Suspense fallback={<LoadingIndicator />}>
        <VPSAdminChat />
      </Suspense>
    </main>
  );
}
