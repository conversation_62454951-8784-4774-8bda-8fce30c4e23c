/**
 * MessageBubble component for displaying individual messages
 */

import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Server, Clock } from 'lucide-react';
import { MessageBubbleProps } from '@/types';
import { SENDER_COLORS, PRIORITY_COLORS, MESSAGE_TYPE_CONFIG } from '@/constants';
import { formatTimestamp } from '@/utils';
import ConfirmationButtons from '@/components/ConfirmationButtons';

const MessageBubble: React.FC<MessageBubbleProps> = ({
  message,
  showConfirmation,
  commandToConfirm,
  isTaskActive,
  onConfirmation
}) => {
  // Determine alignment and bubble styles based on sender and type
  const alignClass = message.sender === 'user' ? 'items-end self-end' : 'items-start self-start';

  // Enhanced bubble styling with priority-based colors
  let bubbleClass = SENDER_COLORS[message.sender].bubble;

  // Override bubble color for high priority messages
  if (message.metadata?.priority === 'critical') {
    bubbleClass = 'bg-gradient-to-br from-red-500 to-red-600 dark:from-red-400 dark:to-red-500 text-white rounded-xl rounded-tl-none shadow-md dark:shadow-lg';
  } else if (message.metadata?.priority === 'high') {
    bubbleClass = 'bg-gradient-to-br from-orange-500 to-orange-600 dark:from-orange-400 dark:to-orange-500 text-white rounded-xl rounded-tl-none shadow-md dark:shadow-lg';
  }

  // Icon configuration
  const getIcon = () => {
    switch (message.sender) {
      case 'user':
        return <User size={20} />;
      case 'ai':
        return <Bot size={20} />;
      default:
        return <Server size={20} />;
    }
  };

  const iconBgClass = SENDER_COLORS[message.sender].icon;

  // Enhanced content styling based on message type and metadata
  let contentStyle = "break-words text-base word-wrap overflow-wrap-anywhere";

  // Type-specific styling
  const typeConfig = MESSAGE_TYPE_CONFIG[message.type as keyof typeof MESSAGE_TYPE_CONFIG];
  if (typeConfig) {
    contentStyle += ` ${typeConfig.borderColor} border-l-4 pl-2`;
  }

  // Special handling for specific message types
  switch (message.type) {
    case 'command_output':
      contentStyle += " w-full text-left max-w-full pl-3";
      break;
    case 'command_request':
      contentStyle += " w-full";
      break;
    case 'question':
      contentStyle += " pl-3";
      break;
  }

  return (
    <div className={`flex flex-col mb-3 lg:mb-4 animate-fadeIn w-full ${alignClass}`}>
      {/* Metadata header for important messages */}
      {(message.metadata?.priority === 'critical' || message.metadata?.priority === 'high') && (
        <div className={`text-xs mb-1 px-2 py-1 rounded-full self-start ${
          PRIORITY_COLORS[message.metadata.priority].bg
        } ${PRIORITY_COLORS[message.metadata.priority].text}`}>
          {message.metadata.priority.toUpperCase()} PRIORITY
        </div>
      )}

      {/* Container for icon + bubble */}
      <div className={`flex items-end gap-1 lg:gap-2 w-full ${
        message.sender === 'user'
          ? 'max-w-[70%] sm:max-w-[60%] lg:max-w-[50%]'
          : 'max-w-[75%] sm:max-w-[65%] lg:max-w-[55%]'
      }`}>
        {/* Icon before bubble for AI/System */}
        {message.sender !== 'user' && (
          <div className={`flex-shrink-0 p-1.5 lg:p-2 rounded-full mb-1 ${iconBgClass}`}>
            {getIcon()}
          </div>
        )}

        {/* The message bubble itself */}
        <div className={`px-3 lg:px-4 py-2 lg:py-3 ${bubbleClass} ${contentStyle} relative min-w-0 flex-1 overflow-hidden`}>
          {message.content}

          {/* Metadata footer */}
          {(message.timestamp || message.metadata?.duration || message.metadata?.retryCount) && (
            <div className="mt-2 pt-2 border-t border-white/20 text-xs opacity-70 flex items-center justify-between">
              <div className="flex items-center gap-2">
                {message.timestamp && (
                  <span className="flex items-center gap-1">
                    <Clock size={10} />
                    <span className="hidden sm:inline">{formatTimestamp(message.timestamp)}</span>
                  </span>
                )}
                {message.metadata?.duration && (
                  <span className="hidden sm:inline">{message.metadata.duration}ms</span>
                )}
              </div>
              {message.metadata?.retryCount && message.metadata.retryCount > 0 && (
                <span className="bg-yellow-500/20 px-1 py-0.5 rounded text-yellow-200">
                  Retry #{message.metadata.retryCount}
                </span>
              )}
            </div>
          )}

          {/* Category badge - positioned at bottom right */}
          {message.metadata?.category && (
            <div className="absolute -bottom-1 -right-1 bg-surface-primary/90 text-theme-secondary text-xs px-2 py-0.5 rounded-full shadow-sm z-10 border border-theme-primary">
              {message.metadata.category}
            </div>
          )}

          {/* Conditionally render confirmation buttons inside AI command request bubble */}
          {message.type === 'command_request' && (
            <ConfirmationButtons
              onConfirm={onConfirmation}
              isVisible={showConfirmation && commandToConfirm === message.rawCommand && isTaskActive}
              options={message.metadata?.options || []}
              showAdvancedOptions={message.content?.toString().includes('failed multiple times') ||
                                 message.content?.toString().includes('Choose an option')}
            />
          )}
        </div>

        {/* Icon after bubble for User */}
        {message.sender === 'user' && (
          <div className={`flex-shrink-0 p-1.5 lg:p-2 rounded-full mb-1 ${iconBgClass}`}>
            {getIcon()}
          </div>
        )}
      </div>
    </div>
  );
};

export default MessageBubble;
