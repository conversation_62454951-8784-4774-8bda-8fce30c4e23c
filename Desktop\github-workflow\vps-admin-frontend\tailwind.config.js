/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
    './src/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  darkMode: 'class',
  theme: {
    extend: {
      // Custom colors using CSS variables
      colors: {
        // Theme colors
        'theme-primary': 'rgb(var(--bg-primary) / <alpha-value>)',
        'theme-secondary': 'rgb(var(--bg-secondary) / <alpha-value>)',
        'theme-tertiary': 'rgb(var(--bg-tertiary) / <alpha-value>)',
        'theme-quaternary': 'rgb(var(--bg-quaternary) / <alpha-value>)',
        
        // Text colors
        'text-theme-primary': 'rgb(var(--text-primary) / <alpha-value>)',
        'text-theme-secondary': 'rgb(var(--text-secondary) / <alpha-value>)',
        'text-theme-tertiary': 'rgb(var(--text-tertiary) / <alpha-value>)',
        'text-theme-quaternary': 'rgb(var(--text-quaternary) / <alpha-value>)',
        
        // Border colors
        'border-theme-primary': 'rgb(var(--border-primary) / <alpha-value>)',
        'border-theme-secondary': 'rgb(var(--border-secondary) / <alpha-value>)',
        'border-theme-tertiary': 'rgb(var(--border-tertiary) / <alpha-value>)',
        
        // Accent colors
        'accent-primary': 'rgb(var(--accent-primary) / <alpha-value>)',
        'accent-secondary': 'rgb(var(--accent-secondary) / <alpha-value>)',
        'accent-tertiary': 'rgb(var(--accent-tertiary) / <alpha-value>)',
        'accent-warning': 'rgb(var(--accent-warning) / <alpha-value>)',
        'accent-error': 'rgb(var(--accent-error) / <alpha-value>)',
        
        // Surface colors
        'surface-primary': 'rgb(var(--surface-primary) / <alpha-value>)',
        'surface-secondary': 'rgb(var(--surface-secondary) / <alpha-value>)',
        'surface-tertiary': 'rgb(var(--surface-tertiary) / <alpha-value>)',
        
        // Interactive colors
        'interactive-primary': 'rgb(var(--interactive-primary) / <alpha-value>)',
        'interactive-secondary': 'rgb(var(--interactive-secondary) / <alpha-value>)',
        'interactive-hover': 'rgb(var(--interactive-hover) / <alpha-value>)',
        
        // Terminal colors
        'terminal-bg': 'rgb(var(--terminal-bg) / <alpha-value>)',
        'terminal-text': 'rgb(var(--terminal-text) / <alpha-value>)',
        'terminal-border': 'rgb(var(--terminal-border) / <alpha-value>)',
        'terminal-success-bg': 'rgb(var(--terminal-success-bg) / <alpha-value>)',
        'terminal-success-text': 'rgb(var(--terminal-success-text) / <alpha-value>)',
        'terminal-success-border': 'rgb(var(--terminal-success-border) / <alpha-value>)',
        'terminal-error-bg': 'rgb(var(--terminal-error-bg) / <alpha-value>)',
        'terminal-error-text': 'rgb(var(--terminal-error-text) / <alpha-value>)',
        'terminal-error-border': 'rgb(var(--terminal-error-border) / <alpha-value>)',
        'terminal-header-bg': 'rgb(var(--terminal-header-bg) / <alpha-value>)',
        'terminal-header-text': 'rgb(var(--terminal-header-text) / <alpha-value>)',
      },
      
      // Custom animations
      animation: {
        'fade-in': 'fadeIn 0.25s ease-out',
        'slide-up': 'slideUp 0.25s ease-out',
        'slide-down': 'slideDown 0.25s ease-out',
        'scale-in': 'scaleIn 0.25s cubic-bezier(0.68, -0.55, 0.265, 1.55)',
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'modal-fade-in': 'modalFadeIn 0.25s ease-out',
        'modal-scale-in': 'modalScaleIn 0.25s ease-out',
        'shimmer': 'shimmer 2s linear infinite',
        'float': 'float 3s ease-in-out infinite',
        'glow': 'glow 2s ease-in-out infinite alternate',
      },
      
      // Custom keyframes
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0', transform: 'translateY(10px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        slideUp: {
          '0%': { opacity: '0', transform: 'translateY(20px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        slideDown: {
          '0%': { opacity: '0', transform: 'translateY(-20px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        scaleIn: {
          '0%': { opacity: '0', transform: 'scale(0.9)' },
          '100%': { opacity: '1', transform: 'scale(1)' },
        },
        modalFadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        modalScaleIn: {
          '0%': { opacity: '0', transform: 'scale(0.95) translateY(-10px)' },
          '100%': { opacity: '1', transform: 'scale(1) translateY(0)' },
        },
        shimmer: {
          '0%': { backgroundPosition: '-200px 0' },
          '100%': { backgroundPosition: 'calc(200px + 100%) 0' },
        },
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' },
        },
        glow: {
          '0%, 100%': { boxShadow: '0 0 5px rgba(var(--color-primary), 0.5)' },
          '50%': { boxShadow: '0 0 20px rgba(var(--color-primary), 0.8)' },
        },
      },
      
      // Custom font families
      fontFamily: {
        'mono': ['Consolas', 'Monaco', 'Courier New', 'monospace'],
      },
      
      // Custom spacing
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
      },
      
      // Custom border radius
      borderRadius: {
        'xl': '0.75rem',
        '2xl': '1rem',
        '3xl': '1.5rem',
      },
      
      // Custom box shadows
      boxShadow: {
        'glow': '0 0 20px rgba(var(--color-primary), 0.3)',
        'glow-success': '0 0 20px rgba(var(--color-success), 0.3)',
        'glow-error': '0 0 20px rgba(var(--color-error), 0.3)',
      },
      
      // Custom backdrop blur
      backdropBlur: {
        'xs': '2px',
      },
    },
  },
  plugins: [
    // Add any additional Tailwind plugins here
  ],
};
