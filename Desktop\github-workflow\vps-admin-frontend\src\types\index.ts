/**
 * Type definitions for VPS Admin Chat application
 */

import React from 'react';

// Enhanced message structure with more types and metadata
export interface Message {
  id: string;
  sender: 'user' | 'ai' | 'system';
  type: 'text' | 'ai_response' | 'command_request' | 'command_output' | 'question' | 'error' | 'info' | 'warning' | 'summary' | 'task_end' | 'task_start' | 'progress' | 'file_operation' | 'security_alert' | 'step_start' | 'step_complete' | 'task_complete';
  content: React.ReactNode; // Can be string or JSX for formatted output
  rawContent?: string; // Store raw text content for markdown processing
  rawCommand?: string; // Store raw command for confirmation buttons
  sshInfo?: SSHInfo; // For command_output
  timestamp?: Date;
  metadata?: MessageMetadata;
}

// SSH command execution result
export interface SSHInfo {
  stdout: string;
  stderr: string;
  exit_status: number;
  success: boolean;
  command?: string;
  execution_time?: number;
}

// Message metadata for enhanced features
export interface MessageMetadata {
  stepNumber?: number;
  totalSteps?: number;
  category?: string;
  priority?: 'low' | 'medium' | 'high' | 'critical';
  duration?: number;
  retryCount?: number;
  securityRisk?: boolean;
  options?: string[]; // Available options for confirmation
  recoverySteps?: any[]; // Recovery steps for error handling
  recoveryApproach?: string; // Recovery approach description
  recoveryStepNumber?: number; // Current recovery step number
  totalRecoverySteps?: number; // Total recovery steps
}

// Enhanced task structure
export interface Task {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'paused';
  progress: number;
  steps: TaskStep[];
  startTime?: Date;
  endTime?: Date;
  totalCommands: number;
  successfulCommands: number;
  failedCommands: number;
}

export interface TaskStep {
  id: string;
  description: string;
  command?: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped';
  result?: any;
  duration?: number;
  retryCount?: number;
}

// Execution statistics
export interface ExecutionStats {
  totalCommands: number;
  successfulCommands: number;
  failedCommands: number;
  averageExecutionTime: number;
  uptime: number;
  lastCommandTime?: Date | null;
}

// API request/response types
export interface StartTaskRequest {
  initial_prompt: string;
  task_metadata?: {
    title?: string;
    description?: string;
    created_at?: string;
    priority?: string;
    category?: string;
  };
}

export interface StartTaskResponse {
  task_id: string;
}

export interface SendMessageRequest {
  task_id: string;
  message: string;
}

// SSE event data types
export interface SSEEventData {
  type: string;
  content: any;
  metadata?: MessageMetadata;
  command?: string;
}

// UI State types
export interface UIState {
  isWaiting: boolean;
  showConfirmation: boolean;
  commandToConfirm: string | null;
  error: string | null;
  isTaskActive: boolean;
  isAwaitingAnswer: boolean;
  showStatsPanel: boolean;
  autoScroll: boolean;
}

// Command history state
export interface CommandHistoryState {
  commandHistory: string[];
  historyIndex: number;
}

// Export data structure
export interface ExportData {
  taskHistory: Task[];
  executionStats: ExecutionStats;
  exportDate: string;
  messages?: Message[];
}

// Component props types
export interface MessageBubbleProps {
  message: Message;
  showConfirmation: boolean;
  commandToConfirm: string | null;
  isTaskActive: boolean;
  onConfirmation: (confirm: 'yes' | 'no' | 'skip_step' | 'retry_step' | 'abort_task') => void;
}

export interface ConfirmationButtonsProps {
  onConfirm: (confirm: 'yes' | 'no' | 'skip_step' | 'retry_step' | 'abort_task') => void;
  isVisible: boolean;
  options?: string[]; // Available options for this confirmation
  showAdvancedOptions?: boolean; // Whether to show skip/retry/abort options
}

export interface StatsPanelProps {
  executionStats: ExecutionStats;
  currentTask: Task | null;
  taskHistory: Task[];
  messages: Message[];
  autoScroll: boolean;
  onClose: () => void;
  onExportHistory: () => void;
  onToggleAutoScroll: () => void;
}

export interface ChatInputProps {
  value: string;
  onChange: (value: string) => void;
  onSubmit: () => void;
  onKeyDown: (event: React.KeyboardEvent<HTMLInputElement>) => void;
  disabled: boolean;
  placeholder: string;
  commandHistory: string[];
  historyIndex: number;
}

export interface ProgressIndicatorProps {
  task: Task;
  className?: string;
}

export interface MarkdownRendererProps {
  content: string;
  className?: string;
}

// Hook return types
export interface UseTaskManagerReturn {
  currentTask: Task | null;
  taskHistory: Task[];
  taskId: string | null;
  isTaskActive: boolean;
  createTask: (title: string, description: string) => Task;
  updateTaskProgress: (taskId: string, progress: number) => void;
  setCurrentTask: (task: Task | null) => void;
  setTaskHistory: (history: Task[]) => void;
  setTaskId: (id: string | null) => void;
  setIsTaskActive: (active: boolean) => void;
}

export interface UseMessageHandlerReturn {
  messages: Message[];
  addMessage: (
    sender: Message['sender'],
    type: Message['type'],
    content: React.ReactNode,
    rawContent?: string,
    sshInfo?: SSHInfo,
    metadata?: MessageMetadata
  ) => void;
  setMessages: (messages: Message[]) => void;
}

export interface UseStreamHandlerReturn {
  handleStream: (taskId: string, message: string) => void;
  abortStream: () => void;
  isStreamActive: () => boolean;
  getStreamState: () => string;
}

export interface UseCommandHistoryReturn {
  commandHistory: string[];
  historyIndex: number;
  addToCommandHistory: (command: string) => void;
  setHistoryIndex: (index: number) => void;
  navigateHistory: (direction: 'up' | 'down') => string;
  clearHistory: () => void;
}

export interface UseStatsReturn {
  executionStats: ExecutionStats;
  updateStats: (type: 'command_success' | 'command_failure', duration?: number) => void;
  resetStats: () => void;
}

// Event handler types
export type ConfirmationHandler = (confirm: 'yes' | 'no' | 'skip_step' | 'retry_step' | 'abort_task') => void;
export type MessageSubmitHandler = (message: string) => void;
export type TaskStartHandler = (prompt: string) => Promise<void>;
export type KeyDownHandler = (event: React.KeyboardEvent<HTMLInputElement>) => void;
