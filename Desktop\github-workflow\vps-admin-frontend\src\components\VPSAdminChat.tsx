// src/components/VPSAdminChat.tsx

'use client';

import React, { useEffect, useRef, useCallback, useState } from 'react';
import { AlertTriangle, CheckCircle, XCircle, Info, HelpCircle, Sparkles, ChevronDown } from 'lucide-react';

// Import existing hooks and components
import { useMessageHandler } from '@/hooks/useMessageHandler';
import { useTaskManager } from '@/hooks/useTaskManager';
import { useCommandHistory } from '@/hooks/useCommandHistory';
import { useStats } from '@/hooks/useStats';
import { useStreamHandler } from '@/hooks/useStreamHandler';

// Import components
import ChatHeader from '@/components/ChatHeader';
import MessageList from '@/components/MessageList';
import ChatInput from '@/components/ChatInput';
import StatsPanel from '@/components/StatsPanel';
import ProgressIndicator from '@/components/ProgressIndicator';
import MarkdownRenderer from '@/components/MarkdownRenderer';
import BackendStatus from '@/components/BackendStatus';
import TerminalOutputWithModal from '@/components/TerminalOutputWithModal';
// Import services
import { apiService } from '@/services/apiService';

// Import types and constants
import { Message, SSHInfo, SSEEventData } from '@/types';

// Import utilities
import { getInputPlaceholder } from '@/utils/messageRendering';
import { exportTaskHistory } from '@/utils';

// Custom hook for UI state management
import { useUIState } from '@/hooks/useUIState';

const VPSAdminChat: React.FC = () => {
  // Initialize hooks
  const { messages, addMessage, setMessages } = useMessageHandler();
  const { taskId, isTaskActive, setTaskId, setIsTaskActive, currentTask, taskHistory, createTask } = useTaskManager();
  const { commandHistory, historyIndex } = useCommandHistory();
  const { executionStats, updateStats } = useStats();

  // UI State management
  const {
    isWaiting,
    showConfirmation,
    commandToConfirm,
    error,
    isAwaitingAnswer,
    showStatsPanel,
    autoScroll,
    setIsWaiting,
    setShowConfirmation,
    setCommandToConfirm,
    setError,
    setIsAwaitingAnswer,
    setShowStatsPanel,
    setAutoScroll
  } = useUIState();

  // Input state
  const [inputMessage, setInputMessage] = useState<string>('');

  // Backend connection state
  const [showBackendStatus, setShowBackendStatus] = useState<boolean>(false);
  const [backendConnectionError, setBackendConnectionError] = useState<boolean>(false);

  // Refs
  const abortControllerRef = useRef<AbortController | null>(null);

  // Cleanup: Abort any ongoing request on component unmount
  useEffect(() => {
    return () => {
      console.log("VPSAdminChat unmounting, aborting any active stream.");
      abortControllerRef.current?.abort();
    };
  }, []);

  // Enhanced addMessage wrapper to handle UI state
  const handleAddMessage = useCallback((
    sender: Message['sender'],
    type: Message['type'],
    content: React.ReactNode,
    rawContent?: string,
    sshInfo?: SSHInfo
  ) => {
    // Set isAwaitingAnswer true ONLY if the new message is a question
    setIsAwaitingAnswer(type === 'question');
    // Ensure showConfirmation is false if adding any message other than command_request
    if(type !== 'command_request') {
        setShowConfirmation(false);
        setCommandToConfirm(null);
    }

    // Use the hook's addMessage function
    addMessage(sender, type, content, rawContent, sshInfo);
   }, [addMessage, setIsAwaitingAnswer, setShowConfirmation, setCommandToConfirm]);

  // Stream event handlers
  const handleStreamMessage = useCallback((data: SSEEventData) => {
    console.log("DEBUG: Stream message received:", data);
    setIsWaiting(true);

    // Process different message types from backend
    if (data.type === 'error') {
      handleAddMessage('system', 'error', <><AlertTriangle size={14} className="inline mr-1 text-accent-error" />{data.content}</>);
    } else if (data.type === 'question') {
      handleAddMessage('ai', 'question', <><HelpCircle size={14} className="inline mr-1 text-accent-tertiary" /><MarkdownRenderer content={data.content} /></>);
      setIsWaiting(false);
    } else if (data.type === 'command_confirmation') {
      handleAddMessage('ai', 'command_request', (
        <div className='w-full'>
          <p className="mb-2 font-medium">🤖 AI proposes the following command:</p>
          <div
            className="terminal-command"
            style={{
              backgroundColor: 'rgb(15, 23, 42)',
              color: 'rgb(226, 232, 240)',
              fontFamily: 'Consolas, Monaco, "Courier New", monospace',
              padding: '0.75rem',
              borderRadius: '0.375rem',
              border: '1px solid rgb(71, 85, 105)',
              position: 'relative',
              whiteSpace: 'pre-wrap',
              wordWrap: 'break-word'
            }}
          >
            {data.content}
          </div>
        </div>
      ), data.content);
      setCommandToConfirm(data.content);
      setShowConfirmation(true);
      setIsWaiting(false);
    } else if (data.type === 'step_skip_option') {
      // Handle step skip option - show advanced confirmation with skip/retry/abort options
      handleAddMessage('ai', 'command_request', (
        <div className='w-full'>
          <p className="mb-2 font-medium text-accent-warning">⚠️ Step has failed multiple times:</p>
          <div className="text-sm text-theme-secondary mb-2">{data.content}</div>
          <div className="text-xs text-theme-tertiary">Choose an option below to continue.</div>
        </div>
      ), data.content);
      setCommandToConfirm(data.content);
      setShowConfirmation(true);
      setIsWaiting(false);
    } else if (data.type === 'task_end') {
      handleAddMessage('system', 'task_end', <><CheckCircle size={14} className="inline mr-1 text-accent-secondary" />{data.content}</>);
      setIsWaiting(false);
      setShowConfirmation(false);
      setCommandToConfirm(null);
      setIsTaskActive(false);
      setIsAwaitingAnswer(false);
    } else if (data.type === 'ssh_output') {
      const sshInfo = data.content as SSHInfo;
      handleAddMessage('system', 'command_output', (
        <TerminalOutputWithModal sshInfo={sshInfo} />
      ), undefined, sshInfo);

      // Update stats
      updateStats(sshInfo?.success ? 'command_success' : 'command_failure', sshInfo?.execution_time);
    } else {
      // Handle other message types
      if (data.type === 'info') {
        handleAddMessage('system', 'info', <><Info size={14} className="inline mr-1 text-accent-primary" /><MarkdownRenderer content={data.content} /></>);
      } else if (data.type === 'warning') {
        handleAddMessage('system', 'warning', <><AlertTriangle size={14} className="inline mr-1 text-accent-warning" /><MarkdownRenderer content={data.content} /></>);
      } else if (data.type === 'summary') {
        handleAddMessage('ai', 'summary', <><Sparkles size={14} className="inline mr-1 text-accent-tertiary" /><MarkdownRenderer content={data.content} /></>);
      } else if (data.type === 'ai_response') {
        // Handle AI responses with markdown rendering
        handleAddMessage('ai', 'ai_response', <MarkdownRenderer content={data.content} />, data.content);
      } else if (data.type === 'progress') {
        // Handle orchestrator progress messages
        handleAddMessage('system', 'progress', <><Info size={14} className="inline mr-1 text-accent-primary" /><MarkdownRenderer content={data.content} /></>);
      } else if (data.type === 'step_start') {
        // Handle step start messages
        handleAddMessage('system', 'step_start', <><Info size={14} className="inline mr-1 text-accent-primary" /><MarkdownRenderer content={data.content} /></>);
      } else if (data.type === 'step_completed' || data.type === 'step_complete') {
        // Handle step completion messages
        handleAddMessage('system', 'step_complete', <><CheckCircle size={14} className="inline mr-1 text-accent-secondary" /><MarkdownRenderer content={data.content} /></>);
      } else if (data.type === 'command_result') {
        // Handle command execution results from orchestrator
        const metadata = data.metadata;
        handleAddMessage('system', 'command_output', (
          <TerminalOutputWithModal sshInfo={metadata} />
        ), undefined, metadata);

        // Update stats
        updateStats(metadata?.success ? 'command_success' : 'command_failure', metadata?.execution_time);
      } else if (data.type === 'command_failed') {
        // Handle command failure messages
        handleAddMessage('system', 'error', <><XCircle size={14} className="inline mr-1 text-accent-error" /><MarkdownRenderer content={data.content} /></>);
      } else if (data.type === 'task_complete') {
        // Handle task completion
        handleAddMessage('system', 'task_complete', <><CheckCircle size={14} className="inline mr-1 text-accent-secondary" /><MarkdownRenderer content={data.content} /></>);
      } else if (data.type === 'task_failed') {
        // Handle task failure
        handleAddMessage('system', 'error', <><XCircle size={14} className="inline mr-1 text-accent-error" /><MarkdownRenderer content={data.content} /></>);
      } else if (data.type === 'task_aborted') {
        // Handle task abortion
        handleAddMessage('system', 'warning', <><AlertTriangle size={14} className="inline mr-1 text-accent-warning" /><MarkdownRenderer content={data.content} /></>);
      } else if (data.type === 'error_recovery_start') {
        // Handle error recovery start
        handleAddMessage('system', 'info', <><AlertTriangle size={14} className="inline mr-1 text-accent-warning" /><MarkdownRenderer content={data.content} /></>);
      } else if (data.type === 'error_recovery_plan') {
        // Handle error recovery plan creation
        const metadata = data.metadata || {};
        handleAddMessage('system', 'info', (
          <div>
            <AlertTriangle size={14} className="inline mr-1 text-accent-warning" />
            <MarkdownRenderer content={data.content} />
            {metadata.recoverySteps && (
              <div className="mt-2 text-sm text-theme-secondary">
                Recovery approach: {metadata.recoveryApproach || 'Unknown'}
              </div>
            )}
          </div>
        ));
      } else if (data.type === 'recovery_step_start') {
        // Handle recovery step start
        const metadata = data.metadata || {};
        handleAddMessage('system', 'info', (
          <div>
            <AlertTriangle size={14} className="inline mr-1 text-accent-warning" />
            <MarkdownRenderer content={data.content} />
            {metadata.recoveryStepNumber && metadata.totalRecoverySteps && (
              <div className="mt-1 text-xs text-theme-tertiary">
                Recovery step {metadata.recoveryStepNumber} of {metadata.totalRecoverySteps}
              </div>
            )}
          </div>
        ));
      } else if (data.type === 'error_recovery_failed') {
        // Handle error recovery failure
        handleAddMessage('system', 'error', <><XCircle size={14} className="inline mr-1 text-accent-error" /><MarkdownRenderer content={data.content} /></>);
      }
    }
  }, [handleAddMessage, setIsWaiting, setShowConfirmation, setCommandToConfirm, setIsTaskActive, setIsAwaitingAnswer, updateStats]);

  const handleStreamError = useCallback((error: Error) => {
    console.error("Stream error:", error);
    setError(error.message);

    // Check if this is a backend connection error
    const isConnectionError = error.message.includes('Cannot connect to backend') ||
                             error.message.includes('Failed to fetch') ||
                             error.message.includes('Backend server is not responding') ||
                             error.message.includes('Backend server error') ||
                             error.message.includes('Connection Error');

    if (isConnectionError) {
      setBackendConnectionError(true);
      setShowBackendStatus(true);
      handleAddMessage('system', 'error', (
        <div className="space-y-2">
          <div>Connection Error: {error.message}</div>
          <div className="text-sm text-theme-secondary">
            The backend connection was lost. Check the "Backend Status" section below.
          </div>
        </div>
      ));
    } else {
      handleAddMessage('system', 'error', `Connection Error: ${error.message}`);
    }

    setIsWaiting(false);
    setShowConfirmation(false);
    setIsAwaitingAnswer(false);
    setIsTaskActive(false);
  }, [handleAddMessage, setError, setIsWaiting, setShowConfirmation, setIsAwaitingAnswer, setIsTaskActive, setBackendConnectionError, setShowBackendStatus]);

  const handleStreamClose = useCallback(() => {
    console.log("Stream closed");
    setIsWaiting(false);
  }, [setIsWaiting]);

  // Initialize stream handler
  const { handleStream: streamHandler } = useStreamHandler(
    handleStreamMessage,
    handleStreamError,
    handleStreamClose
  );

  // Core function to handle streaming communication with the backend
  const handleStream = useCallback((currentTaskId: string, messageToSend: string) => {
    if (!currentTaskId) {
        console.error("handleStream called without task ID.");
        return;
    }

    setError(null);
    setIsWaiting(true);
    setShowConfirmation(false);
    setCommandToConfirm(null);

    console.log(`DEBUG: Starting stream for task ${currentTaskId} with message: ${messageToSend}`);

    // Use the stream handler from the hook
    streamHandler(currentTaskId, messageToSend);

  }, [streamHandler, setError, setIsWaiting, setShowConfirmation, setCommandToConfirm]);


  // --- Start a new task ---
  const startTask = useCallback(async (prompt: string) => {
    const trimmedPrompt = prompt.trim();
    if (!trimmedPrompt) return;

    console.log("INFO: Starting new task...");
    setIsWaiting(true); // Start waiting immediately
    setError(null);
    setMessages([]); // Clear previous messages
    setShowConfirmation(false);
    setCommandToConfirm(null);
    setIsTaskActive(false); // Ensure inactive before start
    setIsAwaitingAnswer(false);
    abortControllerRef.current?.abort(); // Abort any previous stream

    // Add user prompt to UI first
    handleAddMessage('user', 'text', <MarkdownRenderer content={trimmedPrompt} />, trimmedPrompt);
    setInputMessage(''); // Clear input immediately

    try {
      // Use the API service to start the task
      const data = await apiService.startTask({ initial_prompt: trimmedPrompt });
      const newTaskId = data.task_id;

      // Create task in task manager
      createTask(newTaskId, trimmedPrompt);
      setTaskId(newTaskId);
      setIsTaskActive(true);

      console.log(`INFO: Task ${newTaskId} started successfully.`);

      // Send the initial prompt to the stream endpoint to kick off AI processing
      handleStream(newTaskId, trimmedPrompt);

    } catch (err: any) {
      console.error("ERROR: Failed to start task:", err);
      const errorMsg = `Failed to start task: ${err.message}`;
      setError(errorMsg);

      // Check if this is a backend connection error
      const isConnectionError = err.message.includes('Cannot connect to backend') ||
                               err.message.includes('Failed to fetch') ||
                               err.message.includes('Backend server is not responding') ||
                               err.message.includes('Backend server error');

      if (isConnectionError) {
        setBackendConnectionError(true);
        setShowBackendStatus(true);
        handleAddMessage('system', 'error', (
          <div className="space-y-2">
            <div>{errorMsg}</div>
            <div className="text-sm text-theme-secondary">
              Click the "Backend Status" button below to diagnose the connection issue.
            </div>
          </div>
        ));
      } else {
        handleAddMessage('system', 'error', errorMsg);
      }

      setIsWaiting(false); // Stop waiting on error
      setIsTaskActive(false); // Remain inactive
    }
  }, [handleAddMessage, setIsWaiting, setError, setIsTaskActive, setIsAwaitingAnswer, setShowConfirmation, setCommandToConfirm, setTaskId, handleStream, createTask, setInputMessage, setMessages]);

  // --- Send a message within an active task ---
  const handleSendMessage = useCallback((message: string) => {
    const trimmedMessage = message.trim();
    // Can send if task is active AND we are NOT waiting for stream AND NOT showing confirmation buttons
    const canSend = isTaskActive && !isWaiting && !showConfirmation;
    if (!trimmedMessage || !taskId || !canSend) {
        console.log("WARN: Send message blocked:", {isTaskActive, isWaiting, showConfirmation, taskId, message: trimmedMessage});
        return;
    }

    console.log(`INFO: Sending message for task ${taskId}: ${trimmedMessage}`);
    handleAddMessage('user', 'text', <MarkdownRenderer content={trimmedMessage} />, trimmedMessage);
    setInputMessage(''); // Clear input after adding
    handleStream(taskId, trimmedMessage); // Send message to backend stream
  }, [isTaskActive, isWaiting, showConfirmation, taskId, handleAddMessage, setInputMessage, handleStream]);

  // --- Handle confirmation (Yes/No/Skip/Retry/Abort) ---
  const handleConfirmation = useCallback((confirm: 'yes' | 'no' | 'skip_step' | 'retry_step' | 'abort_task') => {
    // Can confirm if task is active and confirmation is showing
    if (!taskId || !commandToConfirm || !isTaskActive || !showConfirmation) {
        console.log("WARN: Confirmation blocked:", {isTaskActive, showConfirmation, taskId});
        return;
    }

    console.log(`INFO: Handling confirmation '${confirm}' for task ${taskId}`);

    // Show user's choice in chat with appropriate formatting
    let displayText = confirm.toUpperCase();
    if (confirm === 'skip_step') displayText = 'SKIP STEP';
    else if (confirm === 'retry_step') displayText = 'RETRY STEP';
    else if (confirm === 'abort_task') displayText = 'ABORT TASK';

    handleAddMessage('user', 'text', displayText); // Show user's choice in chat

    // Hide buttons immediately after click
    setShowConfirmation(false);
    setCommandToConfirm(null);

    // Send the confirmation to the backend stream
    handleStream(taskId, confirm);
  }, [taskId, commandToConfirm, isTaskActive, showConfirmation, handleAddMessage, setShowConfirmation, setCommandToConfirm, handleStream]);

   // Handle Enter key press in input field
   const handleKeyDown = useCallback((event: React.KeyboardEvent<HTMLInputElement>) => {
     if (event.key === 'Enter' && !event.shiftKey) {
       event.preventDefault(); // Prevent newline in input
       // Determine action based on current state
       if (!isTaskActive) { // If no task is active, pressing Enter starts a new task
            startTask(inputMessage);
       } else { // If a task is active...
           // Send message only if NOT waiting AND NOT confirming
           if (!isWaiting && !showConfirmation) {
                handleSendMessage(inputMessage);
           }
           // Otherwise, Enter does nothing (user needs to click Yes/No or wait)
       }
     }
   }, [isTaskActive, inputMessage, isWaiting, showConfirmation, startTask, handleSendMessage]);

  // Get dynamic placeholder text
  const placeholder = getInputPlaceholder(isTaskActive, isWaiting, showConfirmation, isAwaitingAnswer);

  // --- Main JSX Render ---
  return (
    <div className="flex flex-col lg:flex-row h-screen lg:h-[85vh] w-full max-w-7xl gap-0 lg:gap-4 transition-all duration-300 relative">
      {/* Main Chat Container */}
      <div className="flex flex-col flex-1 bg-theme-primary rounded-none lg:rounded-xl shadow-none lg:shadow-2xl border-0 lg:border border-theme-primary overflow-hidden min-h-0 transition-all duration-300">
        {/* Header */}
        <ChatHeader
          isTaskActive={isTaskActive}
          taskId={taskId}
          showStatsPanel={showStatsPanel}
          onToggleStats={() => setShowStatsPanel(!showStatsPanel)}
        />

        {/* Progress Indicator */}
        {currentTask && (
          <ProgressIndicator
            task={currentTask}
            className="px-3 py-2 border-b border-theme-primary transition-all duration-300"
          />
        )}

        {/* Message List */}
        <MessageList
          messages={messages}
          showConfirmation={showConfirmation}
          commandToConfirm={commandToConfirm}
          isTaskActive={isTaskActive}
          isWaiting={isWaiting}
          isAwaitingAnswer={isAwaitingAnswer}
          onConfirmation={handleConfirmation}
        />

        {/* Input Area */}
        <div className="p-3 border-t border-theme-primary bg-theme-primary flex-shrink-0 transition-all duration-300">
          {/* Display persistent error messages */}
          {error && !backendConnectionError && <p className="text-accent-error text-xs mb-1.5 text-center">{error}</p>}

          {/* Backend Status Section */}
          {backendConnectionError && (
            <div className="mb-3 space-y-2">
              <div className="flex items-center justify-between">
                <p className="text-accent-error text-xs">{error}</p>
                <button
                  onClick={() => setShowBackendStatus(!showBackendStatus)}
                  className="text-xs bg-accent-primary text-white px-2 py-1 rounded-lg hover:opacity-90 transition-all duration-200"
                >
                  {showBackendStatus ? 'Hide' : 'Show'} Backend Status
                </button>
              </div>

              {showBackendStatus && (
                <div className="max-h-64 overflow-y-auto">
                  <BackendStatus
                    onStatusChange={(isOnline) => {
                      if (isOnline) {
                        setBackendConnectionError(false);
                        setShowBackendStatus(false);
                        setError(null);
                      }
                    }}
                    autoRefresh={true}
                    refreshInterval={10000} // Check every 10 seconds when shown
                  />
                </div>
              )}
            </div>
          )}

          <ChatInput
            value={inputMessage}
            onChange={setInputMessage}
            onSubmit={() => {
              if (!isTaskActive) {
                startTask(inputMessage);
              } else if (isTaskActive && !isWaiting && !showConfirmation) {
                handleSendMessage(inputMessage);
              }
            }}
            onKeyDown={handleKeyDown}
            disabled={isWaiting || showConfirmation || backendConnectionError}
            placeholder={backendConnectionError ? "Fix backend connection to continue..." : placeholder}
            commandHistory={commandHistory}
            historyIndex={historyIndex}
          />
        </div>
      </div>

      {/* Stats Panel - Responsive Layout */}
      {showStatsPanel && (
        <div className={`
          ${/* Desktop: Side panel */ ''}
          lg:relative lg:w-80 lg:flex-shrink-0
          ${/* Mobile: Full screen overlay */ ''}
          fixed lg:static inset-0 lg:inset-auto lg:top-0 lg:right-0 lg:h-auto
          w-full lg:w-80 h-full lg:h-auto
          z-50 lg:z-auto
          transform lg:transform-none transition-transform duration-300
          ${showStatsPanel ? 'translate-x-0' : 'translate-x-full lg:translate-x-0'}
        `}>
          <StatsPanel
            executionStats={executionStats}
            currentTask={currentTask}
            taskHistory={taskHistory}
            messages={messages}
            autoScroll={autoScroll}
            onClose={() => setShowStatsPanel(false)}
            onExportHistory={() => {
              console.log('Export history requested');
              exportTaskHistory(taskHistory, executionStats, messages);
            }}
            onToggleAutoScroll={() => setAutoScroll(!autoScroll)}
          />
        </div>
      )}
    </div>
  );
};

export default VPSAdminChat;
