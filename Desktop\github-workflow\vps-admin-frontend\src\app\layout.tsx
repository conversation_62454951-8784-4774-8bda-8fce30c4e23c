import type { <PERSON>ada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { ThemeProvider } from '@/components/providers/ThemeProvider';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'VPS AI Admin',
  description: 'AI-powered VPS administration interface with intelligent task automation',
  keywords: ['VPS', 'AI', 'Admin', 'Automation', 'Server Management'],
  authors: [{ name: 'VPS Admin Team' }],
  creator: 'VPS Admin Team',
  publisher: 'VPS Admin',
  robots: {
    index: false,
    follow: false,
  },
  viewport: {
    width: 'device-width',
    initialScale: 1,
    maximumScale: 1,
  },
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#ffffff' },
    { media: '(prefers-color-scheme: dark)', color: '#0f172a' },
  ],
  manifest: '/manifest.json',
  icons: {
    icon: '/favicon.ico',
    shortcut: '/favicon-16x16.png',
    apple: '/apple-touch-icon.png',
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'http://localhost:3000',
    title: 'VPS AI Admin',
    description: 'AI-powered VPS administration interface',
    siteName: 'VPS AI Admin',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'VPS AI Admin',
    description: 'AI-powered VPS administration interface',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        {/* Theme loading script - prevents flash of light theme */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              (function() {
                try {
                  const stored = localStorage.getItem('vps-admin-storage');
                  if (stored) {
                    const parsed = JSON.parse(stored);
                    const theme = parsed.state?.theme || 'light';
                    if (theme === 'dark') {
                      document.documentElement.classList.add('dark');
                    } else {
                      document.documentElement.classList.remove('dark');
                    }
                  }
                } catch (e) {
                  console.warn('Failed to load theme from localStorage:', e);
                }
              })();
            `,
          }}
        />
      </head>
      <body className={`${inter.className} antialiased`} suppressHydrationWarning>
        <ThemeProvider>
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}
