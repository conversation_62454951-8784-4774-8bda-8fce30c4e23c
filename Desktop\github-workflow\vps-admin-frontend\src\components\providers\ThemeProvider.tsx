'use client';

import { useEffect } from 'react';
import { useTheme } from '@/hooks/useTheme';

interface ThemeProviderProps {
  children: React.ReactNode;
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  // Initialize theme hook to apply dark class to document root
  useTheme();

  useEffect(() => {
    // Enable transitions after initial theme is applied
    // This prevents transition flashing on page load
    const timer = setTimeout(() => {
      document.body.classList.add('theme-transitions-enabled');
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  return <>{children}</>;
}
